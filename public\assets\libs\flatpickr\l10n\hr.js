(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.hr = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Croatian = {
      firstDayOfWeek: 1,
      weekdays: {
          shorthand: ["<PERSON>", "Pon", "<PERSON><PERSON>", "Sri", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"],
          longhand: [
              "Nedjel<PERSON>",
              "Ponedjeljak",
              "Utorak",
              "Srije<PERSON>",
              "Četvrtak",
              "Petak",
              "Subota",
          ],
      },
      months: {
          shorthand: [
              "Sij",
              "Velj",
              "<PERSON>ž<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
          ],
          longhand: [
              "Siječanj",
              "<PERSON>el<PERSON><PERSON><PERSON>",
              "Ož<PERSON><PERSON>",
              "Travanj",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON>panj",
              "Srpanj",
              "Kolovoz",
              "Rujan",
              "Listopad",
              "Studeni",
              "Prosinac",
          ],
      },
      time_24hr: true,
  };
  fp.l10ns.hr = Croatian;
  var hr = fp.l10ns;

  exports.Croatian = Croatian;
  exports.default = hr;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
