/**
 * Swiper 8.4.7
 * Most modern mobile touch slider and framework with hardware accelerated transitions
 * https://swiperjs.com
 *
 * Copyright 2014-2023 <PERSON>
 *
 * Released under the MIT License
 *
 * Released on: January 30, 2023
 */

export { default as Swiper, default } from './core/core.js';
export { default as Virtual } from './modules/virtual/virtual.js';
export { default as Keyboard } from './modules/keyboard/keyboard.js';
export { default as Mousewheel } from './modules/mousewheel/mousewheel.js';
export { default as Navigation } from './modules/navigation/navigation.js';
export { default as Pagination } from './modules/pagination/pagination.js';
export { default as Scrollbar } from './modules/scrollbar/scrollbar.js';
export { default as Parallax } from './modules/parallax/parallax.js';
export { default as Zoom } from './modules/zoom/zoom.js';
export { default as Lazy } from './modules/lazy/lazy.js';
export { default as Controller } from './modules/controller/controller.js';
export { default as A11y } from './modules/a11y/a11y.js';
export { default as History } from './modules/history/history.js';
export { default as HashNavigation } from './modules/hash-navigation/hash-navigation.js';
export { default as Autoplay } from './modules/autoplay/autoplay.js';
export { default as Thumbs } from './modules/thumbs/thumbs.js';
export { default as FreeMode } from './modules/free-mode/free-mode.js';
export { default as Grid } from './modules/grid/grid.js';
export { default as Manipulation } from './modules/manipulation/manipulation.js';
export { default as EffectFade } from './modules/effect-fade/effect-fade.js';
export { default as EffectCube } from './modules/effect-cube/effect-cube.js';
export { default as EffectFlip } from './modules/effect-flip/effect-flip.js';
export { default as EffectCoverflow } from './modules/effect-coverflow/effect-coverflow.js';
export { default as EffectCreative } from './modules/effect-creative/effect-creative.js';
export { default as EffectCards } from './modules/effect-cards/effect-cards.js';