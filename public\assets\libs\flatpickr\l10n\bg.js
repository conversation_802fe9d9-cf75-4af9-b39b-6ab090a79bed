(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.bg = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Bulgarian = {
      weekdays: {
          shorthand: ["Нд", "Пн", "Вт", "Ср", "Чт", "Пт", "Сб"],
          longhand: [
              "Неделя",
              "Понеделник",
              "Вторник",
              "Сряда",
              "Четвъртък",
              "Петък",
              "Събота",
          ],
      },
      months: {
          shorthand: [
              "Яну",
              "Фев",
              "Март",
              "<PERSON><PERSON>р",
              "<PERSON>а<PERSON>",
              "Юни",
              "Юли",
              "Авг",
              "Сеп",
              "Окт",
              "Ное",
              "Дек",
          ],
          longhand: [
              "Януари",
              "Февруари",
              "Март",
              "Април",
              "Май",
              "Юни",
              "Юли",
              "Август",
              "Септември",
              "Октомври",
              "Ноември",
              "Декември",
          ],
      },
      time_24hr: true,
      firstDayOfWeek: 1,
  };
  fp.l10ns.bg = Bulgarian;
  var bg = fp.l10ns;

  exports.Bulgarian = Bulgarian;
  exports.default = bg;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
