@extends('admin.master')

@section('content')
    <div class="py-3 d-flex align-items-sm-center flex-sm-row flex-column">
        <div class="flex-grow-1">
            <h4 class="fs-18 fw-semibold m-0">Marque</h4>
        </div>

        <div class="text-end">
            <ol class="breadcrumb m-0 py-0">
                <li class="breadcrumb-item"><a href="javascript: void(0);">Marque</a></li>
                <li class="breadcrumb-item active"> Liste</li>
            </ol>
        </div>
    </div>
    {{-- Validation Errors --}}
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="row">
        <div class="col-12">
            <livewire:brands />
        </div>
    </div>
@endsection
@section('scripts')
    <script>
        document.addEventListener('livewire:init', function() {
            Livewire.on('show-toast', e => {
                toastr.options = {
                    "positionClass": "toast-top-right",
                    "timeOut": "3000"
                };
                const allowed = ['success', 'info', 'warning', 'error'];
                if (allowed.includes(e.type) && typeof toastr[e.type] === 'function') {
                    toastr[e.type](e.message);
                } else {
                    toastr.info(e.message); // fallback
                }
            });
        });
    </script>
@endsection
