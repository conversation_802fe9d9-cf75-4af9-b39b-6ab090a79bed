(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.sq = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Albanian = {
      weekdays: {
          shorthand: ["<PERSON>", "<PERSON>ë", "<PERSON>", "<PERSON><PERSON>", "En", "Pr", "Sh"],
          longhand: [
              "<PERSON> Diel",
              "E Hënë",
              "E Martë",
              "E Mërkurë",
              "E Enj<PERSON>",
              "E Premte",
              "E Shtunë",
          ],
      },
      months: {
          shorthand: [
              "Jan",
              "Shk",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "Dhj",
          ],
          longhand: [
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "Tetor",
              "Nëntor",
              "Dhjetor",
          ],
      },
      time_24hr: true,
  };
  fp.l10ns.sq = Albanian;
  var sq = fp.l10ns;

  exports.Albanian = Albanian;
  exports.default = sq;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
