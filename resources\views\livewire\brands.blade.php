<div>
    <div class="container-fluid">
        <div class="card">

            <div class="card-header">
                <div class="d-flex align-items-center justify-content-between gap-3">
                    <h5 class="card-title mb-0">Liste des marques</h5> <!-- Titre de la carte -->
                    <input wire:model.debounce.400ms="search" class="form-control form-control-sm me-2 w-25" type="search"
                        placeholder="Rechercher par nom" aria-label="Search" />
                    <button class="btn btn-secondary btn-sm" wire:click="create()">Ajouter une marque</button>
                </div>
            </div><!-- end card header -->

            <div class="card-body">

                <table class="table table-bordered dt-responsive nowrap border-end">
                    <thead>
                        <tr>
                            <th>#</th>
                            <th>Nom</th>
                            <th>Déscription</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if ($brands && count($brands))
                            @foreach ($brands as $brand)
                                <tr>
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $brand->name }}</td>
                                    <td>{{ $brand->description }}</td>
                                    <td>
                                        <a wire:click="edit({{ $brand->id }})"><i
                                                class="mdi mdi-pencil text-muted fs-18 rounded-2 border p-1 me-1"></i></a>
                                        <a href="javascript:void(0);" onclick="confirmDelete({{ $brand->id }})"><i
                                                class="mdi mdi-delete text-muted fs-18 rounded-2 border p-1"></i></a>
                                    </td>
                                </tr>
                            @endforeach
                        @else
                            <tr>
                                <td colspan="4" class="text-center">Aucune marque trouvée.</td>
                            </tr>
                        @endif
                    </tbody>
                </table>
                @if ($brands)
                    {{ $brands->links() }}
                @endif
            </div><!-- end card body -->
        </div><!-- end card -->

        <!-- Bootstrap Modal -->
        <div class="modal fade" id="crudModal" tabindex="-1" aria-hidden="true" wire:ignore.self>
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">{{ $brand_id ? 'Modifier une marque' : 'Ajouter une marque' }}</h5>
                        <button wire:click="closeModal" class="btn-close"></button>
                    </div>
                    <div class="modal-body">
                        <form>
                            <div class="mb-3">
                                <label for="name">Nom</label>
                                <input type="text" class="form-control" id="name" placeholder="Saisir le nom"
                                    wire:model="name">
                                @error('name')
                                    <span class="text-danger">{{ $message }}</span>
                                @enderror
                            </div>
                            <div class="mb-3">
                                <label for="description">Déscription</label>
                                <textarea class="form-control" id="description" wire:model="description" placeholder="Saisir la déscription"></textarea>
                            </div>
                            <button type="button" class="btn btn-secondary" wire:click="closeModal">Fermer</button>
                            <button type="button" class="btn btn-primary"
                                wire:click.prevent="store()">Sauvegarder</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Script to handle modal show/hide -->
        <script>
            function confirmDelete(id) {
                if (confirm('Êtes-vous sûr?')) {
                    Livewire.dispatch('deleteBrand', {
                        id: id
                    });
                }
            }

            document.addEventListener('livewire:init', function() {
                Livewire.on('openModal', () => {
                    $('#crudModal').modal('show');
                });

                Livewire.on('closeModal', () => {
                    $('#crudModal').modal('hide');
                });

                $('#crudModal').on('hidden.bs.modal', function() {
                    Livewire.dispatch('resetModal');
                });

                // Ecouteur pour la suppression
                Livewire.on('deleteBrand', e => {
                    @this.delete(e.id);
                });
            });
        </script>
    </div>
</div>
