<?php

namespace App\Livewire;

use App\Models\Brand;
use Livewire\Component;
use Livewire\WithPagination;

class Brands extends Component
{
    use WithPagination;
    protected $paginationTheme = 'bootstrap';
    public $name, $description, $brand_id;
    public $isModalOpen = false;

    public $search = '';


    protected $listeners = ['resetModal' => 'resetCreateForm', 'show-toast'];

    public function render()
    {
        $brands = Brand::where('name', 'like', '%' . $this->search . '%')->orderBy('created_at', 'desc')->paginate(10);
        return view('livewire.brands', compact('brands'));
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function create()
    {
        $this->resetCreateForm();
        $this->openModal();
    }

    public function openModal()
    {
        $this->isModalOpen = true;
        $this->dispatch('openModal');
    }

    public function closeModal()
    {
        $this->isModalOpen = false;
        $this->dispatch('closeModal');
    }

    public function resetCreateForm()
    {
        $this->name = '';
        $this->description = '';
        $this->brand_id = null;
    }

    public function store()
    {
        $this->validate([
            'name' => 'required',
            'description' => 'nullable',
        ]);

        Brand::updateOrCreate(['id' => $this->brand_id], [
            'name' => $this->name,
            'description' => $this->description,
        ]);

        $this->closeModal();
        $this->resetCreateForm();
        $this->dispatch('show-toast', type: 'success', message: 'Brand saved successfully!');
    }

    public function edit($id)
    {
        $brand = Brand::findOrFail($id);
        $this->brand_id = $id;
        $this->name = $brand->name;
        $this->description = $brand->description;

        $this->openModal();
    }

    public function delete($id)
    {
        Brand::find($id)->delete();
        $this->dispatch('show-toast', type: 'success', message: 'Brand deleted successfully!');
    }
}
