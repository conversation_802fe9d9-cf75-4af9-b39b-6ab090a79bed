(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.fi = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Finnish = {
      firstDayOfWeek: 1,
      weekdays: {
          shorthand: ["<PERSON>", "<PERSON>", "Ti", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"],
          longhand: [
              "<PERSON><PERSON><PERSON>i",
              "<PERSON><PERSON>nta<PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "Keski<PERSON>ik<PERSON>",
              "Torstai",
              "Perjan<PERSON>",
              "Lauantai",
          ],
      },
      months: {
          shorthand: [
              "Tammi",
              "He<PERSON>i",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON>säkuu",
              "Heinäkuu",
              "Elokuu",
              "Syyskuu",
              "Lokakuu",
              "Marraskuu",
              "Joulukuu",
          ],
      },
      ordinal: function () {
          return ".";
      },
      time_24hr: true,
  };
  fp.l10ns.fi = Finnish;
  var fi = fp.l10ns;

  exports.Finnish = Finnish;
  exports.default = fi;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
