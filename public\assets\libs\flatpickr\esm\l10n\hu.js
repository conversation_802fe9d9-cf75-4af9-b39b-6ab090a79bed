const fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export const Hungarian = {
    firstDayOfWeek: 1,
    weekdays: {
        shorthand: ["V", "H", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Csütörtök",
            "Péntek",
            "Szombat",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Nov",
            "Dec",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>z<PERSON>",
            "<PERSON>zept<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "November",
            "December",
        ],
    },
    ordinal: function () {
        return ".";
    },
    weekAbbreviation: "Hét",
    scrollTitle: "<PERSON><PERSON><PERSON><PERSON>",
    toggleTitle: "Kattintson a váltáshoz",
    rangeSeparator: " - ",
    time_24hr: true,
};
fp.l10ns.hu = Hungarian;
export default fp.l10ns;
