const fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export const Romanian = {
    weekdays: {
        shorthand: ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>ă<PERSON>ă",
        ],
    },
    months: {
        shorthand: [
            "<PERSON>",
            "Feb",
            "Mar",
            "Apr",
            "<PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Aug",
            "Sep",
            "Oct",
            "Noi",
            "Dec",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "August",
            "Sept<PERSON>brie",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
        ],
    },
    firstDayOfWeek: 1,
    time_24hr: true,
    ordinal: () => {
        return "";
    },
};
fp.l10ns.ro = Romanian;
export default fp.l10ns;
