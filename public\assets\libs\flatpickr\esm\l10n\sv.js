const fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export const Swedish = {
    firstDayOfWeek: 1,
    weekAbbreviation: "v",
    weekdays: {
        shorthand: ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"],
        longhand: [
            "<PERSON><PERSON>ndag",
            "<PERSON>åndag",
            "Tisdag",
            "Onsdag",
            "Torsdag",
            "Fredag",
            "Lördag",
        ],
    },
    months: {
        shorthand: [
            "Jan",
            "Feb",
            "Mar",
            "Apr",
            "Maj",
            "Jun",
            "Jul",
            "Aug",
            "Sep",
            "Okt",
            "Nov",
            "Dec",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>",
            "April",
            "<PERSON>",
            "Juni",
            "Jul<PERSON>",
            "Augusti",
            "September",
            "Oktober",
            "November",
            "December",
        ],
    },
    time_24hr: true,
    ordinal: () => {
        return ".";
    },
};
fp.l10ns.sv = Swedish;
export default fp.l10ns;
