const fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export const Finnish = {
    firstDayOfWeek: 1,
    weekdays: {
        shorthand: ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "Torsta<PERSON>",
            "Perjan<PERSON>",
            "Lauantai",
        ],
    },
    months: {
        shorthand: [
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "Jo<PERSON>",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON>ukoku<PERSON>",
            "<PERSON><PERSON><PERSON>ku<PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON>ak<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
        ],
    },
    ordinal: () => {
        return ".";
    },
    time_24hr: true,
};
fp.l10ns.fi = Finnish;
export default fp.l10ns;
