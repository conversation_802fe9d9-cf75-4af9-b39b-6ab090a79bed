<?php

namespace App\Http\Controllers;

use App\Models\Item;
use App\Models\Order;
use App\Models\Client;
use App\Models\Supplier;
use App\Models\ItemReception;

class DashboardController extends Controller
{
    public function index()
    {

        $Nb_supplier = Supplier::count();
        $Nb_order = Order::count();

        $Nb_client = Client::count();
        $Nb_item = Item::count();

        // Calcul du montant total de toutes les commandes
        $Order_amount = Order::sum('montant');

        return view('admin.dashboard', compact('Nb_supplier', 'Nb_order', 'Nb_client', 'Nb_item', 'Order_amount'));
    }
    public function updateAllOrderAmounts()
    {
        $orders = Order::with('lots.items')->get();
        foreach ($orders as $order) {
            $grandTotal = 0;
            foreach ($order->lots as $lot) {
                foreach ($lot->items as $item) {
                    $grandTotal += $item->quantity * $item->price;
                }
            }
            $order->montant = $grandTotal;
            $order->save();
        }
        return 'Tous les montants de commandes ont été recalculés et mis à jour.';
    }
}
