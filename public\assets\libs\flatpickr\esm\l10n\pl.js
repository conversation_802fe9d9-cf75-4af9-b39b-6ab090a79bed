const fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export const Polish = {
    weekdays: {
        shorthand: ["Nd", "Pn", "Wt", "Śr", "<PERSON><PERSON>", "Pt", "So"],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON>",
            "Ponied<PERSON>ł<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Czwartek",
            "Piątek",
            "Sobota",
        ],
    },
    months: {
        shorthand: [
            "<PERSON><PERSON>",
            "Lut",
            "<PERSON>",
            "<PERSON>wi",
            "<PERSON>",
            "<PERSON><PERSON>",
            "Lip",
            "<PERSON>e",
            "<PERSON>rz",
            "<PERSON><PERSON>",
            "<PERSON>s",
            "<PERSON>ru",
        ],
        longhand: [
            "Stycz<PERSON>ń",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "Kwiec<PERSON>ń",
            "Maj",
            "Czerwiec",
            "Lipiec",
            "Sierpień",
            "<PERSON>rzesi<PERSON>ń",
            "Październik",
            "Listopad",
            "<PERSON>rudzień",
        ],
    },
    rangeSeparator: " do ",
    weekAbbreviation: "tydz.",
    scrollTitle: "Prz<PERSON><PERSON><PERSON>, aby zwi<PERSON>",
    toggleTitle: "<PERSON><PERSON><PERSON><PERSON>, aby prze<PERSON>",
    firstDayOfWeek: 1,
    time_24hr: true,
    ordinal: () => {
        return ".";
    },
};
fp.l10ns.pl = Polish;
export default fp.l10ns;
