(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports) :
  typeof define === 'function' && define.amd ? define(['exports'], factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.et = {}));
}(this, (function (exports) { 'use strict';

  var fp = typeof window !== "undefined" && window.flatpickr !== undefined
      ? window.flatpickr
      : {
          l10ns: {},
      };
  var Estonian = {
      weekdays: {
          shorthand: ["P", "E", "T", "K", "N", "R", "L"],
          longhand: [
              "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "Lau<PERSON>äev",
          ],
      },
      months: {
          shorthand: [
              "Jaan",
              "Veebr",
              "<PERSON><PERSON><PERSON>",
              "Apr",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "Sept",
              "Ok<PERSON>",
              "<PERSON>",
              "Det<PERSON>",
          ],
          longhand: [
              "<PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON><PERSON><PERSON>",
              "<PERSON><PERSON><PERSON>",
              "<PERSON><PERSON>",
              "<PERSON>",
              "<PERSON><PERSON>",
              "<PERSON><PERSON>",
              "August",
              "September",
              "Oktoober",
              "November",
              "Detsember",
          ],
      },
      firstDayOfWeek: 1,
      ordinal: function () {
          return ".";
      },
      weekAbbreviation: "Näd",
      rangeSeparator: " kuni ",
      scrollTitle: "Keri, et suurendada",
      toggleTitle: "Klõpsa, et vahetada",
      time_24hr: true,
  };
  fp.l10ns.et = Estonian;
  var et = fp.l10ns;

  exports.Estonian = Estonian;
  exports.default = et;

  Object.defineProperty(exports, '__esModule', { value: true });

})));
