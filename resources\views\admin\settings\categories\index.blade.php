@extends('admin.master')
@section('content')
    <div class="py-3 d-flex align-items-sm-center flex-sm-row flex-column">
        <div class="flex-grow-1">
            <h4 class="fs-18 fw-semibold m-0">Catégorie</h4>
        </div>

        <div class="text-end">
            <ol class="breadcrumb m-0 py-0">
                <li class="breadcrumb-item"><a href="javascript: void(0);">Catégorie</a></li>
                <li class="breadcrumb-item active"> Liste</li>
            </ol>
        </div>
    </div>
    {{-- Validation Errors --}}
    @if ($errors->any())
        <div class="alert alert-danger">
            <ul class="mb-0">
                @foreach ($errors->all() as $error)
                    <li>{{ $error }}</li>
                @endforeach
            </ul>
        </div>
    @endif
    <div class="row">
        <div class="col-12">
            <div class="card">

                <div class="card-header">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="card-title mb-0">Liste</h5>
                        <button type="button" class="btn btn-secondary btn-sm" data-bs-toggle="modal"
                            data-bs-target="#categoryCreateModal">
                            Ajouter une Catégorie
                        </button>
                    </div>
                </div><!-- end card header -->

                <div class="card-body">
                    <table id="datatable" class="table table-bordered dt-responsive nowrap border-end">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Nom</th>
                                <th>Déscription</th>
                                <th>Action</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($categories as $category)
                                <tr role="row" class="odd">
                                    <td>{{ $loop->iteration }}</td>
                                    <td>{{ $category->name }}</td>
                                    <td>{{ $category->description }}</td>
                                    <td class="d-flex align-items-center justify-content-center">
                                        <a href="javascript:void(0);" style="cursor: pointer"><i
                                                class="mdi mdi-pencil text-muted fs-18 rounded-2 border p-1 me-1"
                                                data-bs-toggle="modal" data-bs-target="#editCategoryModal"
                                                id="{{ $category->id }}"
                                                onclick="editCategory({{ $category->id }})"></i></a>
                                        <form id="delete-form-{{ $category->id }}"
                                            action="{{ route('categories.destroy', $category->id) }}" method="POST"
                                            style="display: inline-block;">
                                            @csrf
                                            @method('DELETE')
                                            <a href="javascript:void(0);" onclick="deleteCategory({{ $category->id }})"
                                                style="cursor: pointer">
                                                <i class="mdi mdi-delete text-muted fs-18 rounded-2 border p-1"></i>
                                            </a>
                                        </form>
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>
    <!-- Create Modal -->
    <div class="modal fade" id="categoryCreateModal" tabindex="-1" aria-labelledby="standard-modalLabel"
        aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="standard-modalLabel">Ajouter une Catégorie</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('categories.store') }}" method="POST">
                        @csrf
                        <div class="mb-3">
                            <label for="name" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="name" name="name">
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Déscription</label>
                            <textarea class="form-control" id="description" name="description"></textarea>
                        </div>
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal fade" id="editCategoryModal" tabindex="-1" aria-labelledby="standard-modalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h1 class="modal-title fs-5" id="standard-modalLabel">Modifier une Catégorie</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="{{ route('categories.update', ':id') }}" method="POST">
                        @csrf
                        @method('PUT')
                        <div class="mb-3">
                            <label for="name" class="form-label">Nom</label>
                            <input type="text" class="form-control" id="name" name="name">
                        </div>
                        <div class="mb-3">
                            <label for="description" class="form-label">Déscription</label>
                            <textarea class="form-control" id="description" name="description"></textarea>
                        </div>
                        <button type="button" class="btn btn-light" data-bs-dismiss="modal">Annuler</button>
                        <button type="submit" class="btn btn-primary">Enregistrer</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <script>
        function editCategory(id) {
            $.ajax({
                url: "{{ route('categories.edit', ':id') }}".replace(':id', id),
                type: 'GET',
                success: function(data) {
                    $('#editCategoryModal').find('form').attr('action',
                        "{{ route('categories.update', ':id') }}"
                        .replace(':id', id));
                    $('#editCategoryModal').find('input[name="name"]').val(data.name);
                    $('#editCategoryModal').find('textarea[name="description"]').val(data.description);
                }
            });
        }

        function deleteCategory(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cette Catégorie?')) {
                document.getElementById('delete-form-' + id).submit();
            }
        }
    </script>
@endsection
