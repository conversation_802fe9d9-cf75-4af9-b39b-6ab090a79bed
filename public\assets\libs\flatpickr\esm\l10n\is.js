const fp = typeof window !== "undefined" && window.flatpickr !== undefined
    ? window.flatpickr
    : {
        l10ns: {},
    };
export const Icelandic = {
    weekdays: {
        shorthand: ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"],
        longhand: [
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
            "Fi<PERSON><PERSON>dagur",
            "<PERSON><PERSON><PERSON><PERSON>gu<PERSON>",
            "Laugardagur",
        ],
    },
    months: {
        shorthand: [
            "Jan",
            "Feb",
            "<PERSON>",
            "Apr",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON>g<PERSON>",
            "<PERSON>",
            "<PERSON>t",
            "Nóv",
            "Des",
        ],
        longhand: [
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON><PERSON>",
            "<PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON><PERSON><PERSON>",
            "<PERSON>g<PERSON><PERSON>",
            "September",
            "Október",
            "Nóvember",
            "Desember",
        ],
    },
    ordinal: () => {
        return ".";
    },
    firstDayOfWeek: 1,
    rangeSeparator: " til ",
    weekAbbreviation: "vika",
    yearAriaLabel: "Ár",
    time_24hr: true,
};
fp.l10ns.is = Icelandic;
export default fp.l10ns;
